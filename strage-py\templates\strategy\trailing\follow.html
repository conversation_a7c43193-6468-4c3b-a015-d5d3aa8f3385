<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跟单策略配置</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/bootstrap-table.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>

<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-trailing-add">
            <h4 class="form-header h4">跟单策略配置</h4>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">账户名称：</label>
                    <div class="col-sm-8">
                        <select id="exAccountId" name="accountName" onchange="handleAccount(this)" class="form-control">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">当前账户余额：</label>
                    <div class="col-sm-8">
                        <label id="balanceId" class="col-sm-3 control-label"></label>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">平台：</label>
                    <div class="col-sm-8">
                        <select name="platform" class="form-control" width="200">
                            <option value="BINANCE">币安</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">币对：</label>
                    <div class="col-sm-8">
                        <select id="symbolId" name="symbol" class="form-control">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">止损比例%：</label>
                    <div class="col-sm-8">
                        <input name="stopLossRate" class="form-control" width="100" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">杠杆倍数：</label>
                    <div class="col-sm-8">
                        <input name="leverage" id="laverValue" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">交易数量(张)：</label>
                    <div class="col-sm-8">
                        <input name="amount" id="inputValue" oninput="calculateResult()" width="100" type="text"
                            required>
                        <label id="resultLabel">= USDT</label>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">跟单策略类型：</label>
                    <div class="col-sm-8">
                        <select name="followType" class="form-control" width="200">
                            <option value="1">止盈正开</option>
                            <option value="2">止损正开</option>
                            <option value="3">止盈反开</option>
                            <option value="4">止损反开</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">开仓条件(涨幅)：</label>
                    <div class="col-sm-8">
                        <input name="riseOpen" class="form-control" width="100" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">触发条件(下跌)：</label>
                    <div class="col-sm-8">
                        <input name="declineTrigger" class="form-control" width="100" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">开仓条件(回调)：</label>
                    <div class="col-sm-8">
                        <input name="declineCall" class="form-control" width="100" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <input type="hidden" name="strategyType" value="1">
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <input type="hidden" value="{{ trailing_profit.group_id or '' }}" name="groupId" id="groupId">
                </div>
            </div>

            <h4 class="form-header h4">分阶段止盈信息</h4>
            <div class="row">
                <div class="col-xs-12">
                    <button type="button" class="btn btn-white btn-sm" onclick="addRow()"><i class="fa fa-plus">
                            增加</i></button>
                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delRow()"><i class="fa fa-minus">
                            删除</i></button>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- Bootstrap Table -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/bootstrap-table.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Layer -->
    <script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script>
    <!-- jQuery Validate -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='ruoyi/js/common.js') }}"></script>
    <script src="{{ url_for('static', filename='js/table.js') }}"></script>
    <script src="{{ url_for('static', filename='js/operate.js') }}"></script>

    <script>
        let priceMap = new Map();
        let balanceMap = new Map();

        $(document).ready(function () {
            // 加载账户选项
            $.ajax({
                url: "/strategy/account/getOptions",
                type: "GET",
                success: function (response) {
                    let data = response.data;
                    let exAccount = $("#exAccountId");
                    $.each(data, function (index, item) {
                        balanceMap.set(item.accountName, item.balanceFutures);
                        exAccount.append(
                            $("<option>").val(item.accountName).text(item.accountName)
                        );
                    });
                }
            });

            // 加载币对选项
            $.ajax({
                url: "/strategy/symbol/getOptions",
                type: "GET",
                success: function (response) {
                    let data = response.data;
                    let symbolSel = $("#symbolId");
                    $.each(data, function (index, item) {
                        priceMap.set(item.symbol, item.price);
                        symbolSel.append(
                            $("<option>").val(item.symbol).text(item.symbol)
                        );
                    });
                }
            });
        });

        function handleAccount(selectedEle) {
            let selectedValue = selectedEle.value;
            let balanceValue = balanceMap.get(selectedValue);
            let balanceSelect = document.getElementById("balanceId")
            balanceSelect.innerText = balanceValue + "  USDT";
        }

        function calculateResult() {
            let inputValue = document.getElementById("inputValue").value;
            let resultLabel = document.getElementById("resultLabel");
            let laverValue = document.getElementById("laverValue").value;
            let symbol = document.getElementById("symbolId").value;
            let curPrice = priceMap.get(symbol);

            // 计算逻辑
            let result = inputValue / laverValue * curPrice;
            // 显示计算结果
            resultLabel.innerText = " = " + result + " USDT ";
        }

        var prefix = "/strategy/trailing"
        $("#form-trailing-add").validate({
            focusCleanup: true
        });

        var groupId = document.getElementById("groupId").value;
        function submitHandler() {
            if (!/^-?\d+(\.\d+)?$/.test(groupId)) {
                layer.alert("策略组未创建！", { icon: 2 });
                event.preventDefault();
                return false;
            }
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-trailing-add').serialize());
            }
        }

        $(function () {
            var options = {
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'priceGain',
                        align: 'center',
                        title: '触发止盈/止损涨幅',
                        formatter: function (value, row, index) {
                            var html = '<input class="form-control" type="text" name="trailingDetailList[' + index + '].priceGain" value="' + (value || '') + '">';
                            return html;
                        }
                    },
                    {
                        field: 'takeProfit',
                        align: 'center',
                        title: '触发止盈/止损',
                        formatter: function (value, row, index) {
                            var html = '<input class="form-control" type="text" name="trailingDetailList[' + index + '].takeProfit" value="' + (value || '') + '">';
                            return html;
                        }
                    },
                    {
                        field: 'type',
                        align: 'center',
                        title: '止盈/止损',
                        formatter: function (value, row, index) {
                            var html = '<select class="form-control" name="trailingDetailList[' + index + '].type">';
                            html += '<option value="0"' + (value === "0" ? ' selected' : '') + '>止盈</option>';
                            html += '<option value="1"' + (value === "1" ? ' selected' : '') + '>止损</option>';
                            html += '</select>';
                            return html;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + index + '\')"><i class="fa fa-remove"></i>删除</a>';
                        }
                    }]
            };
            $.table.init(options);
        });

        function addRow() {
            var count = $("#bootstrap-table").bootstrapTable('getData').length;
            var row = {
                index: count,
                priceGain: "",
                takeProfit: "",
                type: "0"
            }
            $("#bootstrap-table").bootstrapTable('append', row);
        }
    </script>
</body>

</html>