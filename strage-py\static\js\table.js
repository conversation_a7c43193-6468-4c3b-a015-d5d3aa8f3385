/**
 * 表格操作工具类
 */
(function($) {
    'use strict';

    var table = {
        config: {},
        
        // 初始化表格
        init: function(options) {
            var defaults = {
                id: "bootstrap-table",
                type: 0, // 0 代表bootstrapTable 1代表bootstrapTreeTable
                height: undefined,
                sidePagination: "server",
                sortName: "",
                sortOrder: "asc",
                pagination: true,
                paginationLoop: false,
                pageSize: 10,
                pageList: [10, 25, 50],
                search: false,
                showSearch: false,
                showPageGo: false,
                showRefresh: true,
                showColumns: true,
                showToggle: true,
                showExport: false,
                clickToSelect: false,
                singleSelect: false,
                mobileResponsive: true,
                maintainSelected: false,
                rememberSelected: false,
                fixedColumns: false,
                fixedNumber: 0,
                rightFixedColumns: false,
                rightFixedNumber: 0,
                queryParams: table.queryParams,
                rowStyle: {}
            };
            
            var options = $.extend(defaults, options);
            table.config = options;
            
            $('#' + options.id).bootstrapTable({
                id: options.id,
                url: options.url,                                   // 请求后台的URL（*）
                method: 'post',                                     // 请求方式（*）
                toolbar: '#toolbar',                                // 工具按钮用哪个容器
                striped: true,                                      // 是否显示行间隔色
                cache: false,                                       // 是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: options.pagination,                     // 是否显示分页（*）
                sortable: true,                                     // 是否启用排序
                sortOrder: options.sortOrder,                       // 排序方式
                sortName: options.sortName,                         // 排序字段
                queryParams: options.queryParams,                   // 传递参数（*）
                sidePagination: options.sidePagination,             // 分页方式：client客户端分页，server服务端分页（*）
                pageNumber: 1,                                      // 初始化加载第一页，默认第一页
                pageSize: options.pageSize,                         // 每页的记录行数（*）
                pageList: options.pageList,                         // 可供选择的每页的行数（*）
                search: options.search,                             // 是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                strictSearch: false,
                showColumns: options.showColumns,                   // 是否显示所有的列
                showRefresh: options.showRefresh,                   // 是否显示刷新按钮
                minimumCountColumns: 2,                             // 最少允许的列数
                clickToSelect: options.clickToSelect,               // 是否启用点击选中行
                height: options.height,                             // 行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                uniqueId: "id",                                     // 每一行的唯一标识，一般为主键列
                showToggle: options.showToggle,                     // 是否显示详细视图和列表视图的切换按钮
                cardView: false,                                    // 是否显示详细视图
                detailView: false,                                  // 是否显示父子表
                showExport: options.showExport,                     // 是否显示导出
                exportDataType: "basic",                            // basic', 'all', 'selected'.
                singleSelect: options.singleSelect,                 // 是否单选
                mobileResponsive: options.mobileResponsive,         // 是否支持移动端
                maintainSelected: options.maintainSelected,         // 前端翻页时保持所选的行
                rememberSelected: options.rememberSelected,         // 启用翻页记住前面的选择
                columns: options.columns
            });
        },
        
        // 查询条件
        queryParams: function(params) {
            var search = table.config.search;
            var formData = $("#" + table.config.formId).serializeArray();
            var data = {
                page: params.offset / params.limit + 1,
                limit: params.limit,
                orderByColumn: params.sort,
                isAsc: params.order
            };
            
            if (formData) {
                $.each(formData, function(i, item) {
                    data[item.name] = item.value;
                });
            }
            
            return data;
        },
        
        // 搜索
        search: function() {
            $('#' + table.config.id).bootstrapTable('refresh');
        },
        
        // 刷新
        refresh: function() {
            $('#' + table.config.id).bootstrapTable('refresh');
        },
        
        // 序列号生成
        serialNumber: function(index) {
            var table = $('#' + table.config.id).bootstrapTable('getOptions');
            var pageSize = table.pageSize;
            var pageNumber = table.pageNumber;
            return pageSize * (pageNumber - 1) + index + 1;
        }
    };

    $.table = table;

})(jQuery);
