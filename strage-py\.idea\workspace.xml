<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ea4cbe04-e993-480c-9f6d-5ca72f416f2b" name="更改" comment="service全部完成">
      <change beforePath="$PROJECT_DIR$/strategy/service/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/abstract_account_common.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/abstract_account_common.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/account_bean_factory.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/account_bean_factory.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/binance_base_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/binance_base_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/binance_futures_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/binance_futures_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/binance_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/binance_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/binance_spot_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/binance_spot_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/binance_stream_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/binance_stream_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/ex_account_service_impl.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/ex_account_service_impl.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/i_account_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/i_account_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/i_ex_account_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/i_ex_account_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/i_subscribe_symbol_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/i_subscribe_symbol_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/i_trailing_group_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/i_trailing_group_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/i_trailing_profit_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/i_trailing_profit_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/service_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/service_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/subscribe_symbol_service_impl.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/subscribe_symbol_service_impl.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/trailing_binance_service_impl.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/trailing_binance_service_impl.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/trailing_group_service_impl.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/trailing_group_service_impl.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/strategy/service/trailing_profit_service_impl.py" beforeDir="false" afterPath="$PROJECT_DIR$/strategy/service/trailing_profit_service_impl.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="groupByToolId" value="true" />
    <option name="selectedTabId" value="ProjectErrors" />
    <option name="showPreview" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30JyVma6CNEDycFEFwYI2LgtLKw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.app.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/P1017/strage-py&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunManager">
    <configuration name="app" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="strage-py" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.app" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ea4cbe04-e993-480c-9f6d-5ca72f416f2b" name="更改" comment="" />
      <created>1753359996074</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753359996074</updated>
      <workItem from="1753359997191" duration="7000" />
      <workItem from="1753368508291" duration="4316000" />
      <workItem from="1753377085367" duration="598000" />
      <workItem from="1753454713897" duration="813000" />
      <workItem from="1753633645494" duration="33735000" />
      <workItem from="1753804545324" duration="343000" />
    </task>
    <task id="LOCAL-00001" summary="service">
      <option name="closed" value="true" />
      <created>1753799289550</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753799289550</updated>
    </task>
    <task id="LOCAL-00002" summary="service全部完成">
      <option name="closed" value="true" />
      <created>1753803893442</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753803893442</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="service" />
    <MESSAGE value="service全部完成" />
    <option name="LAST_COMMIT_MESSAGE" value="service全部完成" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/strage_py$app.coverage" NAME="app 覆盖结果" MODIFIED="1753727966578" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>