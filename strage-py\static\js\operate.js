/**
 * 操作工具类
 */
(function ($) {
    'use strict';

    var operate = {
        // 新增
        add: function () {
            var url = $.table.config.createUrl;
            layer.open({
                type: 2,
                area: ['800px', '600px'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: '新增' + $.table.config.modalName,
                content: url,
                btn: ['确定', '关闭'],
                yes: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.submitHandler();
                },
                cancel: function (index) {
                    return true;
                }
            });
        },

        // 编辑
        edit: function (id) {
            if (id == undefined) {
                var rows = $.table.selectColumns("id");
                if (rows.length == 0) {
                    layer.msg("请选择一条记录");
                    return;
                }
                id = rows[0];
            }
            var url = table.config.updateUrl.replace("{id}", id);
            layer.open({
                type: 2,
                area: ['800px', '600px'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: '修改' + table.config.modalName,
                content: url,
                btn: ['确定', '关闭'],
                yes: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.submitHandler();
                },
                cancel: function (index) {
                    return true;
                }
            });
        },

        // 删除
        remove: function (id) {
            var ids = [];
            if (id != undefined) {
                ids.push(id);
            } else {
                var rows = $.table.selectColumns("id");
                if (rows.length == 0) {
                    layer.msg("请选择要删除的数据");
                    return;
                }
                ids = rows;
            }

            layer.confirm('确认要删除选中的' + ids.length + '条数据吗？', {
                icon: 3,
                title: '提示'
            }, function (index) {
                $.ajax({
                    url: table.config.removeUrl,
                    type: 'post',
                    data: {
                        ids: ids.join(',')
                    },
                    success: function (result) {
                        if (result.code == 0) {
                            layer.msg('删除成功');
                            $.table.refresh();
                        } else {
                            layer.msg(result.msg);
                        }
                    }
                });
                layer.close(index);
            });
        },

        // 保存
        save: function (url, data, callback) {
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                success: function (result) {
                    if (result.code == 0) {
                        layer.msg('操作成功');
                        if (callback) {
                            callback(result);
                        } else {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            parent.$.table.refresh();
                        }
                    } else {
                        layer.msg(result.msg);
                    }
                },
                error: function () {
                    layer.msg('操作失败');
                }
            });
        },

        // 创建策略组
        addGroup: function () {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                layer.msg("请选择要创建策略组的数据");
                return;
            }

            layer.confirm('确认要创建策略组吗？', {
                icon: 3,
                title: '提示'
            }, function (index) {
                $.ajax({
                    url: table.config.addGroup,
                    type: 'post',
                    data: {
                        ids: rows.join(',')
                    },
                    success: function (result) {
                        if (result.code == 0) {
                            layer.msg('创建策略组成功');
                            $.table.refresh();
                        } else {
                            layer.msg(result.msg);
                        }
                    }
                });
                layer.close(index);
            });
        },

        // 启动策略组
        startGroup: function () {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                layer.msg("请选择要启动的策略组");
                return;
            }
            if (rows.length > 1) {
                layer.msg("只能选择一个策略组");
                return;
            }

            layer.confirm('确认要启动策略组吗？', {
                icon: 3,
                title: '提示'
            }, function (index) {
                $.ajax({
                    url: table.config.startGroup,
                    type: 'post',
                    data: {
                        id: rows[0]
                    },
                    success: function (result) {
                        if (result.code == 0) {
                            layer.msg('启动策略组成功');
                            $.table.refresh();
                        } else {
                            layer.msg(result.msg);
                        }
                    }
                });
                layer.close(index);
            });
        },

        // 停止策略组
        stopGroup: function () {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                layer.msg("请选择要停止的策略组");
                return;
            }
            if (rows.length > 1) {
                layer.msg("只能选择一个策略组");
                return;
            }

            layer.confirm('确认要停止策略组吗？', {
                icon: 3,
                title: '提示'
            }, function (index) {
                $.ajax({
                    url: table.config.stopGroup,
                    type: 'post',
                    data: {
                        id: rows[0]
                    },
                    success: function (result) {
                        if (result.code == 0) {
                            layer.msg('停止策略组成功');
                            $.table.refresh();
                        } else {
                            layer.msg(result.msg);
                        }
                    }
                });
                layer.close(index);
            });
        },

        // 跟单策略配置
        follow: function (id) {
            if (id == undefined) {
                var rows = $.table.selectColumns("id");
                if (rows.length == 0) {
                    layer.msg("请选择一条记录");
                    return;
                }
                id = rows[0];
            }
            var url = table.config.follow.replace("{id}", id);
            layer.open({
                type: 2,
                area: ['800px', '600px'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: '跟单策略配置',
                content: url,
                btn: ['确定', '关闭'],
                yes: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.submitHandler();
                },
                cancel: function (index) {
                    return true;
                }
            });
        }
    };

    $.operate = operate;

})(jQuery);

// 扩展table对象的方法
$.extend($.table, {
    // 获取选中的列
    selectColumns: function (column) {
        var rows = $('#' + $.table.config.id).bootstrapTable('getSelections');
        if (rows.length == 0) {
            return [];
        }
        var ids = [];
        $.each(rows, function (i, row) {
            ids.push(row[column]);
        });
        return ids;
    }
});

// 表单重置
$.form = {
    reset: function () {
        $('#formId')[0].reset();
        $.table.search();
    }
};

// 表单验证
$.validate = {
    form: function () {
        return true; // 简化版本，实际应该进行表单验证
    }
};
