<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改跟踪止盈</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/bootstrap-table.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>

<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-trailing-edit">
            <h4 class="form-header h4">跟踪止盈信息</h4>
            <input name="id" value="{{ trailing_profit.id }}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">用户名称：</label>
                    <div class="col-sm-8">
                        <input name="accountName" value="{{ trailing_profit.account_name or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">平台：</label>
                    <div class="col-sm-8">
                        <input name="platform" value="{{ trailing_profit.platform or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">币对：</label>
                    <div class="col-sm-8">
                        <input name="symbol" value="{{ trailing_profit.symbol or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">建仓价：</label>
                    <div class="col-sm-8">
                        <input name="openPrice" value="{{ trailing_profit.open_price or '' }}" readonly="true"
                            class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">止损价：</label>
                    <div class="col-sm-8">
                        <input name="stopLossPrice" value="{{ trailing_profit.stop_loss_price or '' }}" readonly="true"
                            class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">交易数量：</label>
                    <div class="col-sm-8">
                        <input name="amount" value="{{ trailing_profit.amount or '' }}" readonly="true"
                            class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">杠杆倍数：</label>
                    <div class="col-sm-8">
                        <input name="leverage" value="{{ trailing_profit.leverage or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">订单方向：</label>
                    <div class="col-sm-8">
                        <input name="positionSide" value="{{ trailing_profit.position_side or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">交易模式：</label>
                    <div class="col-sm-8">
                        <input name="tradeMode" value="{{ trailing_profit.trade_mode or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">状态</label>
                    <div class="col-sm-8">
                        <input name="state" value="{{ trailing_profit.state or '' }}" readonly="true"
                            class="form-control" type="text">
                    </div>
                </div>
            </div>
            <h4 class="form-header h4">分阶段止盈信息</h4>
            <div class="row">
                <div class="col-sm-12">
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- Bootstrap Table -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/bootstrap-table.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Layer -->
    <script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script>
    <!-- jQuery Validate -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='ruoyi/js/common.js') }}"></script>
    <script src="{{ url_for('static', filename='js/table.js') }}"></script>
    <script src="{{ url_for('static', filename='js/operate.js') }}"></script>

    <script>
        var prefix = "/strategy/trailing";
        $("#form-trailing-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-trailing-edit').serialize());
            }
        }

        $(function () {
            // 模拟数据，实际应从后端获取
            var trailingDetailList = {{ trailing_profit.trailing_detail_list | tojson if trailing_profit.trailing_detail_list else '[]'
        }};

        var options = {
            data: trailingDetailList,
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'price_gain',
                    align: 'center',
                    title: '触发止盈/损'
                },
                {
                    field: 'trigger_price',
                    align: 'center',
                    title: '触发止盈/损点位'
                },
                {
                    field: 'take_profit',
                    align: 'center',
                    title: '止盈/损点'
                },
                {
                    field: 'state',
                    align: 'center',
                    title: '状态',
                    formatter: function (value, row, index) {
                        var display = '--';
                        if (value === 0) {
                            display = '未启用';
                        } else if (value == 1) {
                            display = '启用中';
                        }
                        return display;
                    }
                },
                {
                    field: 'type',
                    align: 'center',
                    title: '类型',
                    formatter: function (value, row, index) {
                        var display = '--';
                        if (value === 0) {
                            display = '分段止盈';
                        } else if (value == 1) {
                            display = '分段止损';
                        }
                        return display;
                    }
                }
            ]
        };
        $.table.init(options);
        });
    </script>
</body>

</html>